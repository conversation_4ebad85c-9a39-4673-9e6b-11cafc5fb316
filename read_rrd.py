import rrdtool
import os

rrd_file = 'OracleCCArm.rrd'

if not os.path.exists(rrd_file):
    print(f"Error: RRD file '{rrd_file}' not found.")
else:
    try:
        # Get information about the RRD file
        info = rrdtool.info(rrd_file)
        print(f"RRD File Info for '{rrd_file}':")
        for key, value in info.items():
            print(f"  {key}: {value}")

        # Example of fetching data (you might need to adjust start, end, and resolution)
        # This part is commented out as it requires more specific knowledge of the RRD file's data.
        # You would typically fetch data for a specific time range and consolidation function.
        # For example:
        # start_time = info['last_update'] - 3600 # last hour
        # end_time = info['last_update']
        # step = info['step']
        # data = rrdtool.fetch(rrd_file, 'AVERAGE', start=start_time, end=end_time, resolution=step)
        # print("\nFetched Data:")
        # print(data)

    except rrdtool.error as e:
        print(f"Error reading RRD file: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")