import { test, expect } from '@playwright/test';

test.describe('Speed Test Application', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('http://localhost:8080');
    });

    test('should display initial elements correctly', async ({ page }) => {
        await expect(page.locator('h1')).toHaveText('Speed Test');
        await expect(page.locator('#pingResult')).toHaveText('N/A');
        await expect(page.locator('#downloadResult')).toHaveText('N/A');
        await expect(page.locator('#uploadResult')).toHaveText('N/A');
        await expect(page.locator('#startButton')).toBeVisible();
        await expect(page.locator('#startButton')).toHaveText('Start Test');
    });

    test('should run speed test and display results', async ({ page }) => {
        await page.click('#startButton');

        // Wait for all results to be displayed and not be "Testing..."
        await expect(page.locator('#pingResult')).not.toHaveText('Testing...');
        await expect(page.locator('#downloadResult')).not.toHaveText('Testing...');
        await expect(page.locator('#uploadResult')).not.toHaveText('Testing...');

        // Validate that results are numbers (or can be parsed as numbers)
        const pingResult = await page.locator('#pingResult').textContent();
        const downloadResult = await page.locator('#downloadResult').textContent();
        const uploadResult = await page.locator('#uploadResult').textContent();

        expect(parseFloat(pingResult!)).not.toBeNaN();
        expect(parseFloat(downloadResult!)).not.toBeNaN();
        expect(parseFloat(uploadResult!)).not.toBeNaN();

        // Optional: Add more specific range checks if needed, e.g., expect(parseFloat(downloadResult!)).toBeGreaterThan(0);
    });

    test('should run speed test multiple times and log results', async ({ page }) => {
        const numRuns = 3; // Run the test multiple times to observe fluctuations
        const results: { run: number; ping: number; download: number; upload: number; }[] = [];

        for (let i = 0; i < numRuns; i++) {
            console.log(`--- Test Run ${i + 1} ---`);
            await page.reload(); // Reload page to reset state for each run
            await page.click('#startButton');

            await expect(page.locator('#pingResult')).not.toHaveText('Testing...');
            await expect(page.locator('#downloadResult')).not.toHaveText('Testing...');
            await expect(page.locator('#uploadResult')).not.toHaveText('Testing...');

            const pingResult = await page.locator('#pingResult').textContent();
            const downloadResult = await page.locator('#downloadResult').textContent();
            const uploadResult = await page.locator('#uploadResult').textContent();

            console.log(`Ping: ${pingResult} ms`);
            console.log(`Download: ${downloadResult} Mbps`);
            console.log(`Upload: ${uploadResult} Mbps`);

            results.push({
                run: i + 1,
                ping: parseFloat(pingResult!),
                download: parseFloat(downloadResult!),
                upload: parseFloat(uploadResult!),
            });
        }
        console.log('--- All Test Runs Completed ---');
        console.log(results);
    });
});